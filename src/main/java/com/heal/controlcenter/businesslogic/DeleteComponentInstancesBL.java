package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.DeleteComponentInstancesPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Business logic for deleting component instances.
 * Handles validation, database operations, and Redis cache updates.
 */
@Slf4j
@Service
public class DeleteComponentInstancesBL implements BusinessLogic<DeleteComponentInstancesPojo, UtilityBean<List<ComponentInstanceBean>>, String> {

    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final ComponentInstanceDao componentInstanceDao;
    private final InstanceRepo instanceRepo;

    public DeleteComponentInstancesBL(ClientValidationUtils clientValidationUtils,
                                      ServerValidationUtils serverValidationUtils,
                                      ComponentInstanceDao componentInstanceDao,
                                      InstanceRepo instanceRepo) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.componentInstanceDao = componentInstanceDao;
        this.instanceRepo = instanceRepo;
    }

    /**
     * Performs client-side validation for component instance deletion requests.
     * Ensures the account identifier is valid and the list of instance identifiers is not empty.
     *
     * @param arguments The DeleteComponentInstancesPojo containing instance identifiers and deletion type.
     * @return A UtilityBean containing the validated request body and metadata.
     * @throws ClientException If validation fails (e.g., empty instance identifiers list).
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<DeleteComponentInstancesPojo> clientValidation(Object... arguments) throws ClientException {
        try {
            DeleteComponentInstancesPojo requestBody = (DeleteComponentInstancesPojo) arguments[0];
            String accountIdentifier = (String) arguments[1];

            log.debug("[clientValidation] Start - accountIdentifier: {}, requestBody: {}", accountIdentifier, requestBody);
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            // Validate request body
            if (requestBody == null) {
                throw new ClientException("Request body cannot be null");
            }

            if (requestBody.getInstanceIdentifiers() == null || requestBody.getInstanceIdentifiers().isEmpty()) {
                throw new ClientException("Instance identifiers list cannot be null or empty");
            }

            // Validate each instance identifier
            for (String identifier : requestBody.getInstanceIdentifiers()) {
                if (identifier == null || identifier.trim().isEmpty()) {
                    throw new ClientException("Instance identifier cannot be null or empty");
                }
            }

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            log.debug("[clientValidation] Request params map created: {}", requestParamsMap);

            Map<String, Object> metadata = new HashMap<>();
            metadata.put(Constants.HARD_DELETE, requestBody.isHardDelete());
            log.debug("[clientValidation] Metadata set: {}", metadata);

            UtilityBean<DeleteComponentInstancesPojo> utilityBean = UtilityBean.<DeleteComponentInstancesPojo>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(requestBody)
                    .metadata(metadata)
                    .basicUserDetails(basicUserDetails)
                    .build();

            log.debug("[clientValidation] End - utilityBean: {}", utilityBean);
            return utilityBean;

        } catch (Exception e) {
            log.error("[clientValidation] Error during client validation", e);
            throw new ClientException("Client validation failed: " + e.getMessage());
        }
    }

    /**
     * Performs server-side validation for deleting component instances. 
     * Checks user access, component instance existence, and validates deletion constraints.
     *
     * @param utilityBean UtilityBean containing the request and metadata.
     * @return UtilityBean with validated ComponentInstanceBean objects for deletion.
     * @throws ServerException if validation fails (e.g., instance not found, access denied).
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ComponentInstanceBean>> serverValidation(UtilityBean<DeleteComponentInstancesPojo> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start - utilityBean: {}", utilityBean);
        String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        log.debug("[serverValidation] userId: {}, accountIdentifier: {}", userId, accountIdentifier);
        
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        List<String> instanceIdentifiers = utilityBean.getPojoObject().getInstanceIdentifiers();
        boolean hardDelete = (boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE);

        log.debug("[serverValidation] hardDelete: {}", hardDelete);
        log.debug("[serverValidation] Instance identifiers: {}", instanceIdentifiers);

        List<ComponentInstanceBean> componentInstanceBeanList = new ArrayList<>();

        try {
            // Fetch component instances to validate they exist
            List<ComponentInstanceBean> existingInstances = componentInstanceDao.getComponentInstancesByIdentifiers(instanceIdentifiers, accountId);
            
            if (existingInstances.isEmpty()) {
                throw new ServerException("No component instances found for the provided identifiers");
            }

            // Check if all requested instances exist
            List<String> foundIdentifiers = existingInstances.stream()
                    .map(ComponentInstanceBean::getIdentifier)
                    .toList();
            
            List<String> notFoundIdentifiers = instanceIdentifiers.stream()
                    .filter(id -> !foundIdentifiers.contains(id))
                    .toList();
            
            if (!notFoundIdentifiers.isEmpty()) {
                throw new ServerException("Component instances not found for identifiers: " + notFoundIdentifiers);
            }

            // Validate that instances are active (status = 1) for soft delete
            if (!hardDelete) {
                List<String> inactiveInstances = existingInstances.stream()
                        .filter(instance -> instance.getStatus() != 1)
                        .map(ComponentInstanceBean::getIdentifier)
                        .toList();
                
                if (!inactiveInstances.isEmpty()) {
                    throw new ServerException("Cannot soft delete inactive component instances: " + inactiveInstances);
                }
            }

            componentInstanceBeanList.addAll(existingInstances);

        } catch (HealControlCenterException e) {
            log.error("[serverValidation] Error during server validation", e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }

        Map<String, Object> metadata = new HashMap<>(utilityBean.getMetadata());
        metadata.put(Constants.ACCOUNT, account);

        UtilityBean<List<ComponentInstanceBean>> validatedBean = UtilityBean.<List<ComponentInstanceBean>>builder()
                .requestParams(utilityBean.getRequestParams())
                .pojoObject(componentInstanceBeanList)
                .metadata(metadata)
                .build();

        log.debug("[serverValidation] End - validatedBean: {}", validatedBean);
        return validatedBean;
    }

    /**
     * Processes the deletion of component instances (hard or soft delete) and updates Redis cache.
     *
     * @param utilityBean UtilityBean containing ComponentInstanceBean objects to delete and metadata.
     * @return Success message if deletion is successful.
     * @throws DataProcessingException if any error occurs during deletion or Redis update.
     */
    @Override
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public String process(UtilityBean<List<ComponentInstanceBean>> utilityBean) throws DataProcessingException {
        log.debug("[process] Start - utilityBean: {}", utilityBean);
        List<ComponentInstanceBean> componentInstanceBeanList = utilityBean.getPojoObject();
        boolean hardDelete = (boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE);
        Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);
        String accountIdentifier = account.getIdentifier();
        int accountId = account.getId();

        log.debug("[process] Processing {} component instances for deletion (hardDelete: {})", 
                componentInstanceBeanList.size(), hardDelete);

        try {
            List<String> instanceIdentifiers = componentInstanceBeanList.stream()
                    .map(ComponentInstanceBean::getIdentifier)
                    .toList();

            int deletedCount;
            if (hardDelete) {
                log.info("Performing hard delete for component instances: {}", instanceIdentifiers);
                deletedCount = componentInstanceDao.hardDeleteComponentInstances(instanceIdentifiers, accountId);
            } else {
                log.info("Performing soft delete for component instances: {}", instanceIdentifiers);
                deletedCount = componentInstanceDao.softDeleteComponentInstances(instanceIdentifiers, accountId);
            }

            if (deletedCount == 0) {
                throw new DataProcessingException("No component instances were deleted");
            }

            log.info("Successfully deleted {} component instances", deletedCount);

            // Update Redis cache by removing deleted instances
            deleteComponentInstancesInRedis(accountIdentifier, componentInstanceBeanList);

            String message = String.format("Successfully %s deleted %d component instance(s)", 
                    hardDelete ? "hard" : "soft", deletedCount);
            log.debug("[process] End - message: {}", message);
            return message;

        } catch (HealControlCenterException e) {
            log.error("[process] Error during component instance deletion", e);
            throw new DataProcessingException("Failed to delete component instances: " + e.getMessage());
        } catch (Exception e) {
            log.error("[process] Unexpected error during component instance deletion", e);
            throw new DataProcessingException("Unexpected error during component instance deletion: " + e.getMessage());
        }
    }

    /**
     * Removes component instances from Redis cache.
     *
     * @param accountIdentifier The account identifier
     * @param componentInstances List of component instances to remove from cache
     */
    private void deleteComponentInstancesInRedis(String accountIdentifier, List<ComponentInstanceBean> componentInstances) {
        log.debug("[deleteComponentInstancesInRedis] Start - accountIdentifier: {}, instances count: {}", 
                accountIdentifier, componentInstances.size());
        
        try {
            for (ComponentInstanceBean instance : componentInstances) {
                log.debug("[deleteComponentInstancesInRedis] Removing instance from Redis: {}", instance.getIdentifier());
                instanceRepo.removeInstance(accountIdentifier, instance.getIdentifier());
                log.debug("[deleteComponentInstancesInRedis] Removed instance {} from Redis.", instance.getIdentifier());
            }
            log.debug("[deleteComponentInstancesInRedis] Successfully updated Redis cache for all instances");
        } catch (Exception e) {
            log.error("[deleteComponentInstancesInRedis] Error updating Redis cache", e);
            // Don't throw exception here as the database operation was successful
            // Redis cache inconsistency can be resolved by cache refresh
        }
    }
}
